[{"id": "1", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/cmdevops-ci-schedule-server.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "2", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/cmdevops-ci-schedule-server.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "cmdevops-frontend-flow", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/cmdevops-frontend-flow.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "ruby", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/ruby.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "maven-jdk8", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/maven-jdk8.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "maven-jdk17", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/maven-jdk17.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "ant-jdk8", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/ant-jdk8.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "gradle-jdk8", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/gradle-jdk8.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "gradle-jdk17", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/gradle-jdk17.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "python", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/python.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "cmake", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/cmake.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "cmake-3.10.1", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/cmake-3.10.1.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "lua", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/lua.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "rust", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/rust.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "scala", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/scala.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "p4", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/p4.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "android", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/android.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "msbuild", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/msbuild.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "php", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/php.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "go", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/golang.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "androidTest", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/androidtest.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "gulp", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/gulp.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "grunt", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/grunt.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "npm", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/npm.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}, {"id": "duomokuai", "success": true, "code": "", "message": "", "data": {"httpPath": "http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/duomokuai-parent.git", "sshPath": "", "systemName": "", "systemCode": "12121", "apps": [{"applicationCode": 0, "appName": "323232"}]}}]