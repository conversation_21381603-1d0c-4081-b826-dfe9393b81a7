package com.cmcc.cmdevops.ci.com.cmcc.zzz.util;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONPath;

public class JSONPathTest {
    public static void main(String[] args) {
        String json = """
                {
                  "success": true,
                  "code": "200",
                  "message": "成功",
                  "data": [
                    {
                      "id": 1,
                      "repoKey": "bj-maven-0604",
                      "artiName": "my-artifact",
                      "latestVersion": "1.0.0",
                      "artiPath": "bj-maven-0604/my-artifact",
                      "artiType": 1,
                      "artiSource": 0,
                    }
                  ]
                }""";
        System.out.println(JSONPath.eval(json, "$.data[0].artiName"));
        System.out.println("==================");
        JSONObject jsonObject = JSONObject.parseObject(json);
        String eval1 = (String) JSONPath.eval(jsonObject, "$.data[0].artiPath");
        System.out.println(eval1);

        Integer eval2 = (Integer) JSONPath.eval(jsonObject, "$.data[0].artiType");
        System.out.println(eval2);
    }
}
