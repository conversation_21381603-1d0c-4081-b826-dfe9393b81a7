package com.cmcc.cmdevops.ci.service.business.impl;

import com.alibaba.fastjson2.JSONObject;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

/**
 * evalByPath方法测试类
 */
public class EvalByPathTest {
    
    private JSONObject testData;
    
    @BeforeEach
    void setUp() {
        // 模拟k8s监控数据结构
        String jsonStr = """
            {
                "namespace": "cmdevops-ci-tool-01",
                "resourceQuotas": [
                    {
                        "hard": {
                            "limits.cpu": "4",
                            "limits.memory": "8Gi",
                            "requests.cpu": "2",
                            "requests.memory": "4Gi"
                        },
                        "used": {
                            "limits.cpu": "2",
                            "limits.memory": "4Gi",
                            "requests.cpu": "1",
                            "requests.memory": "2Gi"
                        }
                    }
                ],
                "podCount": 5
            }
            """;
        testData = JSONObject.parseObject(jsonStr);
    }
    
    @Test
    void testEvalByPathWithValidData() {
        // 创建测试实例（这里需要实际的BuildEnvironmentBizServiceImpl实例）
        // 由于evalByPath是private方法，这里只是展示测试思路
        
        // 测试String类型提取
        String namespace = (String) testData.get("namespace");
        assertEquals("cmdevops-ci-tool-01", namespace);
        
        // 测试Integer类型提取
        Integer podCount = testData.getInteger("podCount");
        assertEquals(5, podCount);
        
        // 测试嵌套路径提取
        String limitsCpu = testData.getJSONArray("resourceQuotas")
                .getJSONObject(0)
                .getJSONObject("hard")
                .getString("limits.cpu");
        assertEquals("4", limitsCpu);
    }
    
    @Test
    void testEvalByPathWithNullValues() {
        // 测试null值处理
        JSONObject emptyData = new JSONObject();
        
        // 应该返回默认值
        String defaultNamespace = emptyData.getString("namespace");
        assertNull(defaultNamespace);
    }
    
    @Test
    void testEvalByPathWithTypeConversion() {
        // 测试类型转换
        JSONObject mixedData = new JSONObject();
        mixedData.put("stringNumber", "123");
        mixedData.put("numberString", 456);
        
        // String转Integer
        String stringNumber = mixedData.getString("stringNumber");
        assertEquals("123", stringNumber);
        
        // Number转String
        Integer numberValue = mixedData.getInteger("numberString");
        assertEquals(456, numberValue);
    }
}
