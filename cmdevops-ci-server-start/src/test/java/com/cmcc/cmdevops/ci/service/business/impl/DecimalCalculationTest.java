package com.cmcc.cmdevops.ci.service.business.impl;

import org.junit.jupiter.api.Test;
import java.math.BigDecimal;
import java.math.RoundingMode;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 小数计算测试类
 */
public class DecimalCalculationTest {
    
    /**
     * 保留指定位数的小数
     */
    private double roundToDecimalPlaces(double value, int scale) {
        if (Double.isNaN(value) || Double.isInfinite(value)) {
            return 0.0;
        }
        return BigDecimal.valueOf(value)
                .setScale(scale, RoundingMode.HALF_UP)
                .doubleValue();
    }

    /**
     * 计算百分比并保留一位小数
     */
    private double calculatePercentage(double numerator, double denominator) {
        if (denominator == 0) {
            return 0.0;
        }
        double percentage = (numerator / denominator) * 100;
        return roundToDecimalPlaces(percentage, 1);
    }

    /**
     * 安全的字符串转整数
     */
    private int safeParseInt(String str, int defaultValue) {
        if (str == null || str.trim().isEmpty()) {
            return defaultValue;
        }
        try {
            // 处理可能包含单位的字符串，如 "4Gi" -> "4"
            String numericPart = str.replaceAll("[^0-9.]", "");
            if (numericPart.isEmpty()) {
                return defaultValue;
            }
            return (int) Double.parseDouble(numericPart);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
    
    @Test
    void testRoundToDecimalPlaces() {
        // 测试保留一位小数
        assertEquals(12.3, roundToDecimalPlaces(12.34, 1));
        assertEquals(12.4, roundToDecimalPlaces(12.35, 1));
        assertEquals(12.0, roundToDecimalPlaces(12.0, 1));
        
        // 测试保留两位小数
        assertEquals(12.35, roundToDecimalPlaces(12.346, 2));
        assertEquals(12.35, roundToDecimalPlaces(12.349, 2));
        
        // 测试异常值
        assertEquals(0.0, roundToDecimalPlaces(Double.NaN, 1));
        assertEquals(0.0, roundToDecimalPlaces(Double.POSITIVE_INFINITY, 1));
    }
    
    @Test
    void testCalculatePercentage() {
        // 测试正常百分比计算
        assertEquals(50.0, calculatePercentage(1, 2));
        assertEquals(33.3, calculatePercentage(1, 3));
        assertEquals(66.7, calculatePercentage(2, 3));
        assertEquals(100.0, calculatePercentage(1, 1));
        
        // 测试除零情况
        assertEquals(0.0, calculatePercentage(1, 0));
        assertEquals(0.0, calculatePercentage(0, 0));
    }
    
    @Test
    void testSafeParseInt() {
        // 测试正常数字
        assertEquals(123, safeParseInt("123", 0));
        assertEquals(0, safeParseInt("0", -1));
        
        // 测试带单位的字符串
        assertEquals(4, safeParseInt("4Gi", 0));
        assertEquals(1024, safeParseInt("1024Mi", 0));
        assertEquals(2000, safeParseInt("2000m", 0));
        
        // 测试异常情况
        assertEquals(100, safeParseInt(null, 100));
        assertEquals(100, safeParseInt("", 100));
        assertEquals(100, safeParseInt("   ", 100));
        assertEquals(100, safeParseInt("abc", 100));
    }
    
    @Test
    void testCpuMemoryUsageRateCalculation() {
        // 模拟实际的CPU使用率计算
        // 假设：总CPU = 4C，已使用CPU = 2000m (即2C)
        int totalCpu = 4;
        int usedCpu = 2000; // 单位：m
        
        // 原来的复杂计算方式
        double oldWay = Math.round((((double) usedCpu / 1000) / (double) totalCpu) * 1000.0) / 10.0;
        
        // 新的简化计算方式
        double newWay = calculatePercentage((double) usedCpu / 1000, totalCpu);
        
        assertEquals(50.0, oldWay);
        assertEquals(50.0, newWay);
        
        // 模拟内存使用率计算
        // 假设：总内存 = 8G，已使用内存 = 3072Mi (即3G)
        int totalMemory = 8;
        int usedMemory = 3072; // 单位：Mi
        
        double memoryUsageRate = calculatePercentage((double) usedMemory / 1024, totalMemory);
        assertEquals(37.5, memoryUsageRate);
    }
}
