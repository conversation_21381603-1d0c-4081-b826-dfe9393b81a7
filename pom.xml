<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cmcc.cmdevops</groupId>
        <artifactId>cmdevops-parent</artifactId>
        <version>1.0.6-SNAPSHOT</version>
    </parent>

    <groupId>com.cmcc.cmdevops.ci</groupId>
    <artifactId>cmdevops-ci-server</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>cmdevops-ci-server-adapter</module>
        <module>cmdevops-ci-server-service</module>
        <module>cmdevops-ci-server-start</module>
        <module>cmdevops-ci-server-dal</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.cmcc.cmdevops.component</groupId>
            <artifactId>uni-tool</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cmcc.cmdevops.open</groupId>
            <artifactId>uni-openapi-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cmcc.cmdevops.component</groupId>
            <artifactId>unified-ability-springboot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cmcc.cmdevops.component</groupId>
            <artifactId>auth-springboot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cmcc.cmdevops.component</groupId>
            <artifactId>cmdevops-starter-application</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>2.0.57</version>
        </dependency>
    </dependencies>
</project>
