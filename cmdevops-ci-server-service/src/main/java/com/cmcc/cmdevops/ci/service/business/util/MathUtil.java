package com.cmcc.cmdevops.ci.service.business.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class MathUtil {

    private static final Logger log = LoggerFactory.getLogger(MathUtil.class);

    /**
     * 保留指定位数的小数
     *
     * @param value 原始数值
     * @param scale 小数位数
     * @return 保留小数后的数值
     */
    public static double roundToDecimalPlaces(double value, int scale) {
        if (Double.isNaN(value) || Double.isInfinite(value)) {
            return 0.0;
        }
        return BigDecimal.valueOf(value)
                .setScale(scale, RoundingMode.HALF_UP)
                .doubleValue();
    }

    /**
     * 计算百分比并保留一位小数
     *
     * @param numerator 分子
     * @param denominator 分母
     * @param scale 百分比小数位数
     * @return 百分比（0-100）
     */
    public static double calculatePercentage(double numerator, double denominator, int scale) {
        if (denominator == 0) {
            return 0.0;
        }
        double percentage = (numerator / denominator) * 100;
        return roundToDecimalPlaces(percentage, scale);
    }

    /**
     * 安全的字符串转整数
     *
     * @param str 字符串
     * @param defaultValue 默认值
     * @return 转换后的整数
     */
    public static int safeParseInt(String str, int defaultValue) {
        if (str == null || str.trim().isEmpty()) {
            return defaultValue;
        }
        try {
            // 处理可能包含单位的字符串，如 "4Gi" -> "4"
            String numericPart = str.replaceAll("[^0-9.]", "");
            if (numericPart.isEmpty()) {
                return defaultValue;
            }
            return (int) Double.parseDouble(numericPart);
        } catch (NumberFormatException e) {
            log.warn("字符串转整数失败: {}, 使用默认值: {}", str, defaultValue);
            return defaultValue;
        }
    }
}
