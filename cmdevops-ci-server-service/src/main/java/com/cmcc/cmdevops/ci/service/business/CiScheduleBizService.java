package com.cmcc.cmdevops.ci.service.business;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.cmcc.cmdevops.ci.service.bo.CiScheduleRequest;
import org.springframework.stereotype.Service;

public interface CiScheduleBizService {

    String startTask(CiScheduleRequest ciScheduleRequest);

    String stopTask(String jobId);

    String deleteTask(String jobId);

    JSONArray queryLog(String jobId);

    JSONArray queryStage(String jobId);

    String queryStageLog(String jobId, String stepId);

    JSONObject getStatus(String jobId);

    JSONObject getArtifact(String jobId);

    JSONObject collectMetric(CiScheduleRequest ciScheduleRequest);

    void cleanBuildCache(String taskId, String jobId);
}
