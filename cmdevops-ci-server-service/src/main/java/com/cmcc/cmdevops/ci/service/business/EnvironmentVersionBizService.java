package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildVersionBO;
import com.cmcc.cmdevops.ci.service.bo.EnvironmentVersionBO;

import java.util.List;

/**
 * 构建版本业务接口
 */
public interface EnvironmentVersionBizService {
    /**
     * 创建构建版本
     * @param buildVersionBO 构建版本数据对象
     */
    void createEnvironmentVersion(EnvironmentVersionBO environmentVersionBO);

    /**
     * 分页查询构建版本列表
     * @param pageRequest 分页请求参数
     * @return 分页响应结果
     */
    PageResponse<List<EnvironmentVersionBO>> pageEnvironmentVersions(PageRequest pageRequest, EnvironmentVersionBO environmentVersionBO);
}
