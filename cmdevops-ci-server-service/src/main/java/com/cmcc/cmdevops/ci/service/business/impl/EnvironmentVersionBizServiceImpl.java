package com.cmcc.cmdevops.ci.service.business.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildVersionAtomService;
import com.cmcc.cmdevops.ci.service.atom.EnvironmentVersionAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildVersionBO;
import com.cmcc.cmdevops.ci.service.bo.EnvironmentVersionBO;
import com.cmcc.cmdevops.ci.service.business.BuildVersionBizService;
import com.cmcc.cmdevops.ci.service.business.EnvironmentVersionBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.dao.BuildVersionDO;
import com.cmcc.cmdevops.ci.service.dao.EnvironmentVersionDO;
import com.cmcc.cmdevops.util.BeanCloner;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 构建版本业务实现类
 */
@Service
public class EnvironmentVersionBizServiceImpl implements EnvironmentVersionBizService {

    private final EnvironmentVersionAtomService environmentVersionAtomService;

    public EnvironmentVersionBizServiceImpl(EnvironmentVersionAtomService environmentVersionAtomService) {
        this.environmentVersionAtomService = environmentVersionAtomService;
    }

    @Override
    public void createEnvironmentVersion(EnvironmentVersionBO environmentVersionBO) {
        EnvironmentVersionDO environmentVersionDO = BeanCloner.clone(environmentVersionBO, EnvironmentVersionDO.class);
        environmentVersionAtomService.save(environmentVersionDO);
    }


    @Override
    public PageResponse<List<EnvironmentVersionBO>> pageEnvironmentVersions(PageRequest pageRequest, EnvironmentVersionBO environmentVersionBO) {
        Page<EnvironmentVersionDO> page = new Page<>(pageRequest.getPageNo(), pageRequest.getPageSize());
        LambdaQueryWrapper<EnvironmentVersionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EnvironmentVersionDO::getEnvironmentId, environmentVersionBO.getEnvironmentId());
        if (EmptyValidator.isNotEmpty(environmentVersionBO.getStartTime()) && EmptyValidator.isNotEmpty(environmentVersionBO.getEndTime())) {
            queryWrapper.between(EnvironmentVersionDO::getCreateTime, environmentVersionBO.getStartTime(), environmentVersionBO.getEndTime());
        }
        if (EmptyValidator.isNotEmpty(environmentVersionBO.getCreateUid())) {
            queryWrapper.eq(EnvironmentVersionDO::getCreateUid, environmentVersionBO.getCreateUid());
        }
        if (EmptyValidator.isNotEmpty(environmentVersionBO.getOpType())) {
            queryWrapper.eq(EnvironmentVersionDO::getOpType, environmentVersionBO.getOpType());
        }
        if (EmptyValidator.isNotEmpty(environmentVersionBO.getOpObject())) {
            queryWrapper.eq(EnvironmentVersionDO::getOpObject, environmentVersionBO.getOpObject());
        }
        Page<EnvironmentVersionDO> result = environmentVersionAtomService.page(page, queryWrapper);
        List<EnvironmentVersionBO> list = BeanCloner.clone(result.getRecords(), EnvironmentVersionBO.class);
        return PageResponse.success(list, result.getTotal(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }
}
