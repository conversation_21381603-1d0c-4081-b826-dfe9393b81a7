package com.cmcc.cmdevops.ci.service.business.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildEnvironmentAtomService;
import com.cmcc.cmdevops.ci.service.atom.BuildEnvironmentMonitorAtomService;
import com.cmcc.cmdevops.ci.service.atom.BuildEnvironmentPermissionAtomService;
import com.cmcc.cmdevops.ci.service.atom.BuildEnvironmentStorageAtomService;
import com.cmcc.cmdevops.ci.service.atom.BuildTaskAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildEnvironmentBO;
import com.cmcc.cmdevops.ci.service.bo.BuildEnvironmentMonitorBO;
import com.cmcc.cmdevops.ci.service.bo.BuildJenkinsNodeBO;
import com.cmcc.cmdevops.ci.service.bo.BuildNodeBO;
import com.cmcc.cmdevops.ci.service.bo.BuildScheduleSnapshotBO;
import com.cmcc.cmdevops.ci.service.bo.CiScheduleRequest;
import com.cmcc.cmdevops.ci.service.bo.EnvironmentVersionBO;
import com.cmcc.cmdevops.ci.service.bo.StatusEnum;
import com.cmcc.cmdevops.ci.service.business.BuildEnvironmentBizService;
import com.cmcc.cmdevops.ci.service.business.BuildJenkinsNodeBizService;
import com.cmcc.cmdevops.ci.service.business.BuildNodeBizService;
import com.cmcc.cmdevops.ci.service.business.CiScheduleBizService;
import com.cmcc.cmdevops.ci.service.business.EnvironmentVersionBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.business.util.JsonUtil;
import com.cmcc.cmdevops.ci.service.business.util.UserUtils;
import com.cmcc.cmdevops.ci.service.dao.BuildEnvironmentDO;
import com.cmcc.cmdevops.ci.service.dao.BuildEnvironmentMonitorDO;
import com.cmcc.cmdevops.ci.service.dao.BuildEnvironmentPermissionDO;
import com.cmcc.cmdevops.ci.service.dao.BuildEnvironmentStorageDO;
import com.cmcc.cmdevops.ci.service.dao.BuildTaskDO;
import com.cmcc.cmdevops.exception.BusinessException;
import com.cmcc.cmdevops.util.BeanCloner;
import com.cmcc.cmdevops.util.DateUtil;
import io.kubernetes.client.custom.IntOrString;
import io.kubernetes.client.custom.Quantity;
import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.openapi.ApiException;
import io.kubernetes.client.openapi.Configuration;
import io.kubernetes.client.openapi.apis.AppsV1Api;
import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.openapi.apis.RbacAuthorizationV1Api;
import io.kubernetes.client.openapi.models.RbacV1Subject;
import io.kubernetes.client.openapi.models.V1ConfigMap;
import io.kubernetes.client.openapi.models.V1Container;
import io.kubernetes.client.openapi.models.V1ContainerPort;
import io.kubernetes.client.openapi.models.V1Deployment;
import io.kubernetes.client.openapi.models.V1DeploymentSpec;
import io.kubernetes.client.openapi.models.V1LabelSelector;
import io.kubernetes.client.openapi.models.V1Namespace;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import io.kubernetes.client.openapi.models.V1PodSpec;
import io.kubernetes.client.openapi.models.V1PodTemplateSpec;
import io.kubernetes.client.openapi.models.V1PolicyRule;
import io.kubernetes.client.openapi.models.V1ResourceRequirements;
import io.kubernetes.client.openapi.models.V1Role;
import io.kubernetes.client.openapi.models.V1RoleBinding;
import io.kubernetes.client.openapi.models.V1RoleRef;
import io.kubernetes.client.openapi.models.V1Service;
import io.kubernetes.client.openapi.models.V1ServiceAccount;
import io.kubernetes.client.openapi.models.V1ServicePort;
import io.kubernetes.client.openapi.models.V1ServiceSpec;
import io.kubernetes.client.util.Config;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.StringReader;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.cmcc.cmdevops.ci.service.business.util.MathUtil.calculatePercentage;

/**
 * 构建环境业务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BuildEnvironmentBizServiceImpl implements BuildEnvironmentBizService {

    @Value("${ci-tool.schedule-image}")
    private String scheduleImage;

    @Value("${ci-tool.tool-image}")
    private String toolImage;

    @Value("${ci-tool.ci-tool-cm.buildkitdToml}")
    private String buildkitdToml;

    @Value("${ci-tool.ci-tool-cm.daemonJson}")
    private String daemonJson;

    private final BuildEnvironmentAtomService buildEnvironmentAtomService;

    private final BuildNodeBizService buildNodeBizService;

    private final BuildJenkinsNodeBizService buildJenkinsNodeBizService;

    private final EnvironmentVersionBizService environmentVersionBizService;

    private final CiScheduleBizService ciScheduleBizService;

    private final BuildEnvironmentMonitorAtomService buildEnvironmentMonitorAtomService;

    private final BuildEnvironmentStorageAtomService buildEnvironmentStorageAtomService;

    private final BuildTaskAtomService buildTaskAtomService;

    private final BuildEnvironmentPermissionAtomService buildEnvironmentPermissionAtomService;

    @Override
    public void save(BuildEnvironmentBO buildEnvironmentBO) {
        checkEnvironmentNameUnique(null, buildEnvironmentBO.getEnvironmentName(), buildEnvironmentBO.getSpaceId());
        BuildEnvironmentDO buildEnvironmentDO = BeanCloner.clone(buildEnvironmentBO, BuildEnvironmentDO.class);
        buildEnvironmentDO.setTenantId(UserUtils.getTenantId());
        buildEnvironmentDO.setCreateUid(UserUtils.getUserId());
        buildEnvironmentDO.setSpaceId("");
        buildEnvironmentDO.setProjectAuth(false);
        buildEnvironmentDO.setSpaceAuth(false);
        buildEnvironmentAtomService.save(buildEnvironmentDO);
        this.saveHistory(new BuildEnvironmentDO(), buildEnvironmentDO, "create");
    }

    private void saveHistory(BuildEnvironmentDO oldDO, BuildEnvironmentDO newDO, String opType) {
        BuildEnvironmentBO environmentBO = this.detail(newDO.getId());
        EnvironmentVersionBO environmentVersionBO = new EnvironmentVersionBO();
        environmentVersionBO.setEnvironmentId(newDO.getId());
        environmentVersionBO.setOldVersion(JSONObject.toJSONString(oldDO));
        environmentVersionBO.setNewVersion(JSONObject.toJSONString(environmentBO));
        environmentVersionBO.setCreateUid(UserUtils.getUserId());
        environmentVersionBO.setOpType(opType);
        environmentVersionBO.setOpObject("environment");
        environmentVersionBO.setTenantId(UserUtils.getTenantId());
        environmentVersionBizService.createEnvironmentVersion(environmentVersionBO);
    }

    @Override
    public void update(BuildEnvironmentBO buildEnvironmentBO) {
        checkEnvironmentNameUnique(buildEnvironmentBO.getId(), buildEnvironmentBO.getEnvironmentName(), buildEnvironmentBO.getSpaceId());
        BuildEnvironmentBO environmentBO = this.detail(buildEnvironmentBO.getId());
        BuildEnvironmentDO oldDO = BeanCloner.clone(environmentBO, BuildEnvironmentDO.class);
        BuildEnvironmentDO buildEnvironmentDO = BeanCloner.clone(buildEnvironmentBO, BuildEnvironmentDO.class);
        buildEnvironmentDO.setUpdateUid(UserUtils.getUserId());
        buildEnvironmentDO.setSpaceId(buildEnvironmentAtomService.getById(buildEnvironmentBO.getId()).getSpaceId());
        buildEnvironmentAtomService.updateById(buildEnvironmentDO);
        this.saveHistory(oldDO, buildEnvironmentDO, "update");
    }

    private void checkEnvironmentNameUnique(Integer id, String name, String spaceId) {
        LambdaQueryWrapper<BuildEnvironmentDO> queryWrapper = new LambdaQueryWrapper<>();
        if (EmptyValidator.isNotEmpty(id)) {
            queryWrapper.ne(BuildEnvironmentDO::getId, id);
        }
        queryWrapper.eq(BuildEnvironmentDO::getEnvironmentName, name);
        long count = buildEnvironmentAtomService.count(queryWrapper);
        if (count > 0) {
            throw new BusinessException("已存在该名称的构建环境");
        }
    }

    @Override
    public void delete(Integer id) {
        BuildEnvironmentBO environmentBO = this.detail(id);
        LambdaQueryWrapper<BuildEnvironmentPermissionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BuildEnvironmentPermissionDO::getType, "user");
        queryWrapper.eq(BuildEnvironmentPermissionDO::getBusinessDataId, UserUtils.getUserId());
        queryWrapper.eq(BuildEnvironmentPermissionDO::getEnvironmentId, id);
        List<BuildEnvironmentPermissionDO> list = buildEnvironmentPermissionAtomService.list(queryWrapper);
        if (!environmentBO.getCreateUid().equals(UserUtils.getUserId()) || EmptyValidator.isEmpty(list)) {
            throw new BusinessException("无权限删除改构建环境!");
        }

        LambdaQueryWrapper<BuildTaskDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BuildTaskDO::getAssignedNode, id);
        wrapper.in(BuildTaskDO::getBuildStatus, StatusEnum.QUEUED, StatusEnum.RUNNING);
        List<BuildTaskDO> list1 = buildTaskAtomService.list(wrapper);
        if (EmptyValidator.isNotEmpty(list1)) {
            throw new BusinessException("已有构建任务正在使用该环境，无法删除!");
        }
        buildEnvironmentAtomService.removeById(id);

        EnvironmentVersionBO environmentVersionBO = new EnvironmentVersionBO();
        environmentVersionBO.setEnvironmentId(id);
        environmentVersionBO.setOldVersion(JSONObject.toJSONString(environmentBO));
        environmentVersionBO.setNewVersion(JSONObject.toJSONString(new EnvironmentVersionBO()));
        environmentVersionBO.setCreateUid(UserUtils.getUserId());
        environmentVersionBO.setOpType("delete");
        environmentVersionBO.setOpObject("environment");
        environmentVersionBO.setTenantId(UserUtils.getTenantId());
        environmentVersionBizService.createEnvironmentVersion(environmentVersionBO);
    }

    @Override
    public BuildEnvironmentBO detail(Integer id) {
        BuildEnvironmentDO buildEnvironmentDO = buildEnvironmentAtomService.getById(id);
        return (buildEnvironmentDO != null) ? BeanCloner.clone(buildEnvironmentDO, BuildEnvironmentBO.class) : null;
    }

    @Override
    public PageResponse<List<BuildEnvironmentBO>> page(PageRequest pageRequest, BuildEnvironmentBO buildEnvironmentBO) {
        Page<BuildEnvironmentDO> page = new Page<>(pageRequest.getPageNo(), pageRequest.getPageSize());
        LambdaQueryWrapper<BuildEnvironmentDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(BuildEnvironmentDO::getCreateTime);
        queryWrapper.eq(BuildEnvironmentDO::getEnvironmentSource, buildEnvironmentBO.getEnvironmentSource());
        if (EmptyValidator.isNotEmpty(buildEnvironmentBO)) {
            if (EmptyValidator.isNotEmpty(buildEnvironmentBO.getEnvironmentName())) {
                queryWrapper.like(BuildEnvironmentDO::getEnvironmentName, buildEnvironmentBO.getEnvironmentName());
            }
            if (EmptyValidator.isNotEmpty(buildEnvironmentBO.getEnvironmentType())) {
                queryWrapper.eq(BuildEnvironmentDO::getEnvironmentType, buildEnvironmentBO.getEnvironmentType());
            }
            if (EmptyValidator.isNotEmpty(buildEnvironmentBO.getAccelerationNode())) {
                queryWrapper.eq(BuildEnvironmentDO::getAccelerationNode, buildEnvironmentBO.getAccelerationNode());
            }
            if (EmptyValidator.isNotEmpty(buildEnvironmentBO.getStatus())) {
                queryWrapper.eq(BuildEnvironmentDO::getStatus, buildEnvironmentBO.getStatus());
            }
//            if (EmptyValidator.isNotEmpty(buildEnvironmentBO.getSpaceId())) {
//                queryWrapper.eq(BuildEnvironmentDO::getSpaceId, buildEnvironmentBO.getSpaceId());
//            }
        }
        Page<BuildEnvironmentDO> result = buildEnvironmentAtomService.page(page, queryWrapper);
        List<BuildEnvironmentBO> list = BeanCloner.clone(result.getRecords(), BuildEnvironmentBO.class);
        return PageResponse.success(list, result.getTotal(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }

    @Override
    public void init(Integer environmentId) {
        ApiClient client = null;
        BuildEnvironmentBO buildEnvironmentBO = this.detail(environmentId);
        String kubeconfigConfig = buildEnvironmentBO.getEnvironmentConfig();
        try {
            client = Config.fromConfig(new StringReader(kubeconfigConfig));
        } catch (Exception e) {
            throw new BusinessException("环境配置的kubeConfig异常");
        }
        // 创建调度服务
        if (EmptyValidator.isEmpty(buildEnvironmentBO.getSpaceId())) {
            try {
                log.info("创建调度服务开始");
                createNamespace(client, "cmdevops-ci");
                createDeploymentWithOptionalSA(client, "cmdevops-ci", "cmdevops-ci-schedule-server", scheduleImage, 2, 8080, null, "1", "4", "1024Mi", "4096Mi");
                BuildEnvironmentBizServiceImpl.createNodePortService(client, "cmdevops-ci", "cmdevops-ci-schedule-server", "cmdevops-ci-schedule-server", "NodePort", 8080);
                log.info("创建调度服务结束");
                // 创建命名空间，cmdevops-ci
                // 循环创建 3个 cmdevops-ci-tool-xxx
                for (int i = 0; i < 3; i++) {
                    String namespace = "cmdevops-ci-tool-0" + (i + 1);
                    String sa = "cmdevops-ci-tool-sa";
                    // 创建命名空间
                    createNamespace(client, namespace);
                    createServiceAccountWithPodFullAccess(client, namespace, sa);
                    createDeploymentWithOptionalSA(client, namespace, "cmdevops-ci-tool-master", toolImage, 1, 8080, "cmdevops-ci-tool-sa", "1", "8", "2048Mi", "8192Mi");
                    BuildEnvironmentBizServiceImpl.createNodePortService(client, namespace, "ci-master", "cmdevops-ci-tool-master", "NodePort", 8080, 50000);
                    Map<String, String> data = new HashMap<>();
                    data.put("buildkitd.toml", buildkitdToml);
                    data.put("daemon.json", daemonJson);
                    BuildEnvironmentBizServiceImpl.createConfigMap(client, namespace, "inbound-agent-config", data);

                    BuildJenkinsNodeBO buildJenkinsNodeBO = new BuildJenkinsNodeBO();
                    buildJenkinsNodeBO.setName(namespace);
                    buildJenkinsNodeBO.setType("kubernetes");
                    buildJenkinsNodeBO.setNodeStatus("2");
                    buildJenkinsNodeBO.setNodeStartTime(LocalDateTime.now());
                    buildJenkinsNodeBO.setBuildEnvironmentId(buildEnvironmentBO.getId());
                    buildJenkinsNodeBO.setNodeUrl("http://ci-master." + namespace + ":8080");
                    buildJenkinsNodeBO.setTenantId(UserUtils.getTenantId());
                    buildJenkinsNodeBO.setCreateUid(UserUtils.getUserId());
                    buildJenkinsNodeBizService.createBuildJenkinsNode(buildJenkinsNodeBO);
                }
                BuildNodeBO buildNodeBO = new BuildNodeBO();
                buildNodeBO.setBuildEnvironmentId(buildEnvironmentBO.getId());
                buildNodeBO.setBuildNodeType("kubernetes");
                buildNodeBO.setBuildNodeName(buildEnvironmentBO.getEnvironmentName());
                buildNodeBO.setBuildNodeStatus("2");
                buildNodeBO.setTenantId(UserUtils.getTenantId());
                buildNodeBO.setCreateUid(UserUtils.getUserId());
                buildNodeBizService.createBuildNode(buildNodeBO);
            } catch (Exception e) {
                throw new BusinessException("缓存初始化异常");
            }
            buildEnvironmentBO.setSpaceId("init");
            BuildEnvironmentDO buildEnvironmentDO = BeanCloner.clone(buildEnvironmentBO, BuildEnvironmentDO.class);
            buildEnvironmentAtomService.updateById(buildEnvironmentDO);
        } else {
            throw new BusinessException("环境已初始化");
        }

    }

    // 集群采集
    @Override
    public void collectMetric() {
        // 采集批次
        String collectBatchId = System.currentTimeMillis() + "";
        // 命名空间监控信息
        List<BuildEnvironmentMonitorDO> buildEnvironmentMonitorDOS = new ArrayList<>();
        // minio存储使用信息
        List<BuildEnvironmentStorageDO> buildEnvironmentStorageDOS = new ArrayList<>();
        // key: taskId_environmentId_spaceId, value: 单个构建任务存储使用量
        HashMap<String, Integer> storageMap = new HashMap<>();

        List<BuildEnvironmentDO> list = buildEnvironmentAtomService.list();
        list.stream().filter(buildEnvironmentDO -> buildEnvironmentDO.getSpaceId().equals("init")).forEach(buildEnvironmentDO -> {
            CiScheduleRequest ciScheduleRequest = new CiScheduleRequest();
            BuildScheduleSnapshotBO buildScheduleSnapshotBO = new BuildScheduleSnapshotBO();

            BuildJenkinsNodeBO buildJenkinsNodeBO = buildJenkinsNodeBizService.getBuildJenkinsNodeById("1");
            buildJenkinsNodeBO.setBuildEnvironmentId(buildEnvironmentDO.getId());
            List<BuildJenkinsNodeBO> buildJenkinsNodeBOS = buildJenkinsNodeBizService.list(buildJenkinsNodeBO);
            List<String> namespaceList = buildJenkinsNodeBOS.stream().map(BuildJenkinsNodeBO::getName).collect(Collectors.toList());

            buildScheduleSnapshotBO = BuildScheduleSnapshotBO.builder()
                    .ciScheduleUrl(buildEnvironmentDO.getEnvironmentUrl())
                    .s3Endpoint(buildEnvironmentDO.getCacheDir())
                    .accessKey(buildEnvironmentDO.getAccessKey())
                    .secretKey(buildEnvironmentDO.getSecretKey())
                    .namespaceList(namespaceList)
                    .build();
            ciScheduleRequest.setBuildTaskDTO(buildScheduleSnapshotBO);
            JSONObject metric = ciScheduleBizService.collectMetric(ciScheduleRequest);
            if (EmptyValidator.isNotEmpty(metric)) {
                // String metricJson = metric.toJSONString();
                Boolean success = metric.getBoolean("success");
                if (!success) {
                    log.error("构建环境监控信息采集失败，environmentId：{}， message：{}", buildEnvironmentDO.getId(), metric.getString("message"));
                    return;
                }
                // 命名空间监控信息
                JSONArray k8sInfoArr = metric.getJSONObject("data").getJSONArray("k8sInfo");
                for (int i = 0; i < k8sInfoArr.size(); i++) {
                    JSONObject k8sInfoObj = k8sInfoArr.getJSONObject(i);
                    BuildEnvironmentMonitorDO buildEnvironmentMonitorDO = new BuildEnvironmentMonitorDO().setEnvironmentId(buildEnvironmentDO.getId())
                            .setCollectBatchId(collectBatchId)
                            .setNamespace(JsonUtil.evalByPath(k8sInfoObj, "$.namespace", String.class, ""))
                            .setHardLimitsCpu(JsonUtil.evalByPath(k8sInfoObj, "$.resourceQuotas[0].hard['limits.cpu']", String.class, "0"))
                            .setHardLimitsMemory(JsonUtil.evalByPath(k8sInfoObj, "$.resourceQuotas[0].hard['limits.memory']", String.class, "0").replace("Gi", ""))
                            .setHardRequestsCpu(JsonUtil.evalByPath(k8sInfoObj, "$.resourceQuotas[0].hard['requests.cpu']", String.class, "0"))
                            .setHardRequestsMemory(JsonUtil.evalByPath(k8sInfoObj, "$.resourceQuotas[0].hard['requests.memory']", String.class, "0").replace("Gi", ""))
                            .setUsedLimitsCpu(JsonUtil.evalByPath(k8sInfoObj, "$.resourceQuotas[0].used['limits.cpu']", String.class, "0").replace("m", ""))
                            .setUsedLimitsMemory(JsonUtil.evalByPath(k8sInfoObj, "$.resourceQuotas[0].used['limits.memory']", String.class, "0"))
                            .setUsedRequestsCpu(JsonUtil.evalByPath(k8sInfoObj, "$.resourceQuotas[0].used['requests.cpu']", String.class, "0").replace("m", ""))
                            .setUsedRequestsMemory(JsonUtil.evalByPath(k8sInfoObj, "$.resourceQuotas[0].used['requests.memory']", String.class, "0").replace("Mi", ""))
                            .setPodCount(JsonUtil.evalByPath(k8sInfoObj, "$.podCount", Integer.class, 0))
                            .setCreateTime(LocalDateTime.now())
                            .setCreateUid(UserUtils.getUserId())
                            .setSpaceId(buildEnvironmentDO.getSpaceId())
                            .setTenantId(UserUtils.getTenantId());
                    buildEnvironmentMonitorDOS.add(buildEnvironmentMonitorDO);
                }
                // minio存储分配率
                JSONArray storageArr = metric.getJSONObject("data").getJSONArray("storage");
                for (int i = 0; i < storageArr.size(); i++) {
                    JSONObject storageObj = storageArr.getJSONObject(i);
                    // 根据objectName截取taskId
                    String objectName = storageObj.getString("objectName");
                    Integer storageSize = storageObj.getInteger("size");
                    String[] objectNameArr = objectName.split("/");
                    String taskId = objectNameArr[0];
                    String uniqueKey = taskId + "_" + buildEnvironmentDO.getId() + "_" + buildEnvironmentDO.getSpaceId();
                    if (storageMap.containsKey(uniqueKey)) {
                        // 同一个构建任务的存储使用量累加
                        storageSize += storageMap.get(uniqueKey);
                    }
                    storageMap.put(uniqueKey, storageSize);
                }
            }
        });
        storageMap.forEach((key, value) -> {
            String[] keyArr = key.split("_");
            BuildEnvironmentStorageDO buildEnvironmentStorageDO = new BuildEnvironmentStorageDO().setEnvironmentId(Integer.parseInt(keyArr[1]))
                    .setTaskId(keyArr[0])
                    .setUsedStorage(value)
                    .setCreateTime(LocalDateTime.now())
                    .setCreateUid(UserUtils.getUserId())
                    .setSpaceId(keyArr[2])
                    .setTenantId(UserUtils.getTenantId());
            buildEnvironmentStorageDOS.add(buildEnvironmentStorageDO);
        });
        buildEnvironmentMonitorAtomService.saveBatch(buildEnvironmentMonitorDOS);
        buildEnvironmentStorageDOS.forEach(each -> {
            LambdaUpdateWrapper<BuildEnvironmentStorageDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(BuildEnvironmentStorageDO::getEnvironmentId, each.getEnvironmentId());
            updateWrapper.eq(BuildEnvironmentStorageDO::getTaskId, each.getTaskId());
            updateWrapper.eq(BuildEnvironmentStorageDO::getSpaceId, each.getSpaceId());
            buildEnvironmentStorageAtomService.update(each, updateWrapper);
        });
    }

    @Override
    public BuildEnvironmentMonitorBO monitor(Integer envId) {
        // 该构建环境下命名空间监控信息
        // 根据构建环境Id查询最近一批采集数据
        List<BuildEnvironmentMonitorDO> monitorDOS = buildEnvironmentMonitorAtomService.listLatestByEnvId(envId);
        // 命名空间总数
        int totalNamespaceCount = monitorDOS.size();
        // 构建任务总数
        LambdaQueryWrapper<BuildTaskDO> buildTaskDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        buildTaskDOLambdaQueryWrapper.eq(BuildTaskDO::getAssignedNode, envId);
        List<BuildTaskDO> taskList = buildTaskAtomService.list(buildTaskDOLambdaQueryWrapper);
        int totalTaskCount = taskList.size();
        // 排队中构建任务数
        long queuedTaskCount = taskList.stream().filter(each -> String.valueOf(StatusEnum.QUEUED.getCode()).equals(each.getBuildStatus())).count();
        // 执行中构建任务数
        int runningTaskCount = monitorDOS.stream().mapToInt(BuildEnvironmentMonitorDO::getPodCount).sum();

        // 集群总CPU，单位：C
        int totalCpu = monitorDOS.stream().mapToInt(each -> Integer.parseInt(each.getHardLimitsCpu())).sum();
        // 集群总内存，单位：G
        int totalMemory = monitorDOS.stream().mapToInt(each -> Integer.parseInt(each.getHardLimitsMemory())).sum();
        // 集群总使用CPU，单位：m
        int usedCpu = monitorDOS.stream().mapToInt(each -> Integer.parseInt(each.getUsedLimitsCpu())).sum();
        // 集群总使用内存，单位：B（字节）
        long usedMemory = monitorDOS.stream().mapToLong(each -> Long.parseLong(each.getUsedLimitsMemory())).sum();
        // 集群CPU分配率（保留一位小数）
        double clusterCpuUsageRate = calculatePercentage((double) usedCpu / 1000, totalCpu, 1);
        // 集群内存分配率（保留一位小数）- 将usedMemory从字节转换为GB与totalMemory单位统一
        double clusterMemoryUsageRate = calculatePercentage((double) usedMemory / (1024 * 1024 * 1024), totalMemory, 1);

        // minio存储总量，2T
        long minioTotalStorage = 2 * 1024L;
        // minio存储使用量，单位：KB
        LambdaQueryWrapper<BuildEnvironmentStorageDO> storageDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        storageDOLambdaQueryWrapper.eq(BuildEnvironmentStorageDO::getEnvironmentId, envId);
        storageDOLambdaQueryWrapper.select(BuildEnvironmentStorageDO::getUsedStorage);
        List<BuildEnvironmentStorageDO> storageList = buildEnvironmentStorageAtomService.list(storageDOLambdaQueryWrapper);
        long minioUsedStorage = storageList.stream()
                .mapToLong(storage -> storage.getUsedStorage() != null ? storage.getUsedStorage() : 0L)
                .sum();
        // minio存储使用量，单位：GB
        long minioUsedStorageGB = minioUsedStorage / (1024 * 1024);
        // 集群minio存储分配率
        double minioStorageUsageRate = calculatePercentage(minioUsedStorageGB , minioTotalStorage, 1);

        BuildEnvironmentMonitorBO buildEnvironmentMonitorBO = new BuildEnvironmentMonitorBO();

        // 命名空间实时运行任务数量统计
        List<BuildEnvironmentMonitorBO.NamespaceTaskBO> namespaceTaskBOS = new ArrayList<>();
        monitorDOS.forEach(each -> {
            BuildEnvironmentMonitorBO.NamespaceTaskBO namespaceTaskBO = buildEnvironmentMonitorBO.new NamespaceTaskBO()
                    .setNamespace(each.getNamespace())
                    .setRunningTaskCount(each.getPodCount());
            namespaceTaskBOS.add(namespaceTaskBO);
        });

        // 命名空间资源实时使用情况列表
        ArrayList<BuildEnvironmentMonitorBO.NamespaceMonitorBO> namespaceMonitorBOS = new ArrayList<>();
        ArrayList<String> abnormalNamespaceMsgs = new ArrayList<>();
        // 正常命名空间数量
        AtomicInteger normalNamespaceCount = new AtomicInteger();
        // 异常命名空间
        AtomicInteger abnormalNamespaceCount = new AtomicInteger();
        monitorDOS.forEach(each -> {
            double cpuUsageRate = calculatePercentage((double) Integer.parseInt(each.getUsedLimitsCpu()) / 1000, Integer.parseInt(each.getHardLimitsCpu()), 1);
            double memoryUsageRate = calculatePercentage((double) Long.parseLong(each.getUsedLimitsMemory()) / (1024 * 1024 * 1024), Long.parseLong(each.getHardLimitsMemory()), 1);
            // 超过95%，则为异常
            int isCpuOverload = BigDecimal.valueOf(cpuUsageRate).compareTo(new BigDecimal(95));
            int isMemoryOverload = BigDecimal.valueOf(memoryUsageRate).compareTo(new BigDecimal(95));
            if (isCpuOverload > 0 || isMemoryOverload > 0) {
                abnormalNamespaceCount.getAndIncrement();
                // 封装异常消息
                StringBuilder abnormalMsg = new StringBuilder();
                String happenTime = DateUtil.format(Timestamp.valueOf(each.getCreateTime()), "HH:mm:ss");
                abnormalMsg.append(happenTime).append("，");
                abnormalMsg.append("命名空间").append(each.getNamespace()).append("：");
                if (isCpuOverload > 0) {
                    abnormalMsg.append("CPU分配率").append(cpuUsageRate).append("%，");
                }
                if (isMemoryOverload > 0) {
                    abnormalMsg.append("内存分配率").append(memoryUsageRate).append("%，");
                }
                abnormalMsg.append("超过阈值95%");
                abnormalNamespaceMsgs.add(abnormalMsg.toString());
            } else {
                normalNamespaceCount.getAndIncrement();
            }
            BuildEnvironmentMonitorBO.NamespaceMonitorBO namespaceMonitorBO = buildEnvironmentMonitorBO.new NamespaceMonitorBO()
                    .setNamespace(each.getNamespace())
                    .setTaskCount(each.getPodCount())
                    .setUsedCpu(String.valueOf(Integer.parseInt(each.getUsedLimitsCpu()) / 1000))
                    .setUsedMemory(String.valueOf(Long.parseLong(each.getUsedLimitsMemory()) / 1024 * 1024 * 1024))
                    .setLimitedCpu(each.getHardLimitsCpu())
                    .setLimitedMemory(each.getHardLimitsMemory())
                    .setCpuUsageRate(cpuUsageRate)
                    .setMemoryUsageRate(memoryUsageRate)
                    .setCpuStatus(isCpuOverload > 0 ? "异常" : "正常")
                    .setMemoryStatus(isMemoryOverload > 0 ? "异常" : "正常");
            namespaceMonitorBOS.add(namespaceMonitorBO);
        });

        buildEnvironmentMonitorBO.setEnvironmentId(envId)
                .setTotalNamespaceCount(totalNamespaceCount)
                .setTotalTaskCount(totalTaskCount)
                .setRunningTaskCount(runningTaskCount)
                .setQueuedTaskCount(Math.toIntExact(queuedTaskCount))
                .setClusterCpuUsageRate(clusterCpuUsageRate)
                .setClusterMemoryUsageRate(clusterMemoryUsageRate)
                .setMinioStorageUsageRate(minioStorageUsageRate)
                .setMinioTotalStorage(minioTotalStorage)
                .setMinioUsedStorage(minioUsedStorageGB)
                .setNamespaceTasks(namespaceTaskBOS)
                .setNamespaceMonitors(namespaceMonitorBOS)
                .setNormalNamespaceCount(normalNamespaceCount.get())
                .setAbnormalNamespaceCount(abnormalNamespaceCount.get())
                .setAbnormalNamespaceMsgs(abnormalNamespaceMsgs);
        return buildEnvironmentMonitorBO;
    }

    private void createNamespace(ApiClient client, String name) throws Exception {

        Configuration.setDefaultApiClient(client);

        CoreV1Api api = new CoreV1Api();

        V1Namespace namespace = new V1Namespace()
                .metadata(new V1ObjectMeta().name(name));

        try {
            V1Namespace result = api.createNamespace(namespace).execute();
            log.info("✅ Namespace created: {}", result.getMetadata().getName());
        } catch (ApiException e) {
            if (e.getCode() == 409) {
                log.info("⚠️ Namespace already exists, skipping: {}", name);
            } else {
                log.error("❌ Failed to create Namespace: {}", e.getResponseBody(), e);
            }
        }
    }

    private void createServiceAccountWithPodFullAccess(ApiClient client, String namespace, String saName) throws Exception {
        Configuration.setDefaultApiClient(client);

        CoreV1Api coreV1Api = new CoreV1Api();
        RbacAuthorizationV1Api rbacApi = new RbacAuthorizationV1Api();

        // 1️⃣ 创建 ServiceAccount
        V1ServiceAccount serviceAccount = new V1ServiceAccount()
                .metadata(new V1ObjectMeta().name(saName).namespace(namespace));

        try {
            coreV1Api.createNamespacedServiceAccount(namespace, serviceAccount).execute();
            log.info("✅ ServiceAccount created: " + saName);
        } catch (ApiException e) {
            if (e.getCode() == 409) {
                log.info("⚠️ ServiceAccount already exists: " + saName);
            } else {
                throw e;
            }
        }

        // 2️⃣ 创建 Role —— 授权对 Pod 的所有操作
        String roleName = saName + "-pod-full-access";
        V1Role role = new V1Role()
                .metadata(new V1ObjectMeta().name(roleName).namespace(namespace))
                .rules(Collections.singletonList(
                        new V1PolicyRule()
                                .apiGroups(Collections.singletonList(""))      // Core API group
                                .resources(Collections.singletonList("pods"))  // pod 资源
                                .verbs(Collections.singletonList("*"))          // 所有操作
                ));
        try {
            rbacApi.createNamespacedRole(namespace, role).execute();
            log.info("✅ Role created: " + roleName);
        } catch (ApiException e) {
            if (e.getCode() == 409) {
                log.info("⚠️ Role already exists: " + roleName);
            } else {
                throw e;
            }
        }

        // 3️⃣ 创建 RoleBinding —— 绑定 Role 到 ServiceAccount
        String roleBindingName = saName + "-binding";
        V1RoleBinding roleBinding = new V1RoleBinding()
                .metadata(new V1ObjectMeta().name(roleBindingName).namespace(namespace))
                .roleRef(new V1RoleRef()
                        .apiGroup("rbac.authorization.k8s.io")
                        .kind("Role")
                        .name(roleName))
                .subjects(Collections.singletonList(
                        new RbacV1Subject()
                                .kind("ServiceAccount")
                                .namespace(namespace)
                                .name(saName)
                ));
        try {
            rbacApi.createNamespacedRoleBinding(namespace, roleBinding).execute();
            log.info("✅ RoleBinding created: " + roleBindingName);
        } catch (ApiException e) {
            if (e.getCode() == 409) {
                log.info("⚠️ RoleBinding already exists: " + roleBindingName);
            } else {
                throw e;
            }
        }
    }

    public void createDeploymentWithOptionalSA(
            ApiClient client,
            String namespace,
            String deploymentName,
            String image,
            int replicas,
            int containerPort,
            String serviceAccountName,
            String cpuRequest,
            String cpuLimit,
            String memoryRequest,
            String memoryLimit
    ) throws Exception {
        Configuration.setDefaultApiClient(client);

        CoreV1Api coreV1Api = new CoreV1Api();
        AppsV1Api appsV1Api = new AppsV1Api();

        boolean saExists = false;
        if (EmptyValidator.isNotEmpty(serviceAccountName)) {
            try {
                coreV1Api.readNamespacedServiceAccount(serviceAccountName, namespace).execute();
                saExists = true;
                log.info("✅ ServiceAccount 存在: " + serviceAccountName);
            } catch (io.kubernetes.client.openapi.ApiException e) {
                if (e.getCode() == 404) {
                    log.info("⚠️ ServiceAccount 不存在: " + serviceAccountName + "，将不绑定");
                } else {
                    throw e;
                }
            }
        }

        // 设置资源 requests/limits
        Map<String, Quantity> requests = new HashMap<>();
        Map<String, Quantity> limits = new HashMap<>();
        if (cpuRequest != null) requests.put("cpu", new Quantity(cpuRequest));
        if (memoryRequest != null) requests.put("memory", new Quantity(memoryRequest));
        if (cpuLimit != null) limits.put("cpu", new Quantity(cpuLimit));
        if (memoryLimit != null) limits.put("memory", new Quantity(memoryLimit));

        V1ResourceRequirements resources = new V1ResourceRequirements()
                .requests(requests)
                .limits(limits);

        V1Container container = new V1Container()
                .name(deploymentName + "-container")
                .image(image)
                .ports(Collections.singletonList(new V1ContainerPort().containerPort(containerPort)))
                .resources(resources);

        V1PodSpec podSpec = new V1PodSpec()
                .containers(Collections.singletonList(container));

        if (saExists) {
            podSpec.setServiceAccountName(serviceAccountName);
        }

        V1Deployment deployment = new V1Deployment()
                .apiVersion("apps/v1")
                .kind("Deployment")
                .metadata(new V1ObjectMeta().name(deploymentName).namespace(namespace))
                .spec(new V1DeploymentSpec()
                        .replicas(replicas)
                        .selector(new V1LabelSelector().matchLabels(Collections.singletonMap("app", deploymentName)))
                        .template(new V1PodTemplateSpec()
                                .metadata(new V1ObjectMeta().labels(Collections.singletonMap("app", deploymentName)))
                                .spec(podSpec)
                        )
                );

        try {
            V1Deployment created = appsV1Api.createNamespacedDeployment(namespace, deployment)
                    .pretty("true")
                    .execute();
            log.info("🎉 Deployment created: " + created.getMetadata().getName());
        } catch (io.kubernetes.client.openapi.ApiException e) {
            if (e.getCode() == 409) {
                log.info("⚠️ Deployment 已存在，跳过创建: " + deploymentName);
            } else {
                log.error("❌ 创建 Deployment 异常: " + e.getResponseBody(), e);
                throw e;
            }
        }
    }

    public static void createNodePortService(ApiClient client, String namespace, String serviceName, String selectorLabel, String svcType, int... ports) throws Exception {
        Configuration.setDefaultApiClient(client);

        CoreV1Api coreV1Api = new CoreV1Api();

        V1ServicePort[] servicePorts = new V1ServicePort[ports.length];

        for (int i = 0; i < ports.length; i++) {
            int port = ports[i];
            String portName = "port-" + port;

            V1ServicePort servicePort = new V1ServicePort()
                    .name(portName)
                    .port(port)
                    .targetPort(new IntOrString(port));

            servicePorts[i] = servicePort;
        }

        V1Service service = new V1Service()
                .apiVersion("v1")
                .kind("Service")
                .metadata(new V1ObjectMeta()
                        .name(serviceName)
                        .namespace(namespace))
                .spec(new V1ServiceSpec()
                        .type("NodePort")
                        .ports(Arrays.asList(servicePorts))
                        .selector(Collections.singletonMap("app", selectorLabel))
                );

        try {
            coreV1Api.createNamespacedService(namespace, service).pretty("true").execute();
            log.info("✅ NodePort Service created: " + serviceName);
        } catch (ApiException e) {
            if (e.getCode() == 409) {
                log.info("⚠️ Service already exists, skipping creation: " + serviceName);
                // 跳过，不执行任何操作
            } else {
                log.error("❌ Error creating Service: " + e.getResponseBody(), e);
                throw e; // 或根据业务需求决定是否继续抛出异常
            }
        }
    }


    public static void createConfigMap(ApiClient client, String namespace, String configMapName, Map<String, String> data) throws Exception {
        Configuration.setDefaultApiClient(client);

        CoreV1Api coreV1Api = new CoreV1Api();

        V1ConfigMap configMap = new V1ConfigMap()
                .apiVersion("v1")
                .kind("ConfigMap")
                .metadata(new V1ObjectMeta().name(configMapName).namespace(namespace))
                .data(data);

        try {
            coreV1Api.createNamespacedConfigMap(namespace, configMap).pretty("true").execute();
            log.info("✅ ConfigMap created: " + configMapName);
        } catch (ApiException e) {
            if (e.getCode() == 409) { // Resource conflict, indicates that the resource already exists
                log.warn("⚠️ ConfigMap " + configMapName + " already exists. Attempting update...");
                // Perform update logic here, for example:
                coreV1Api.replaceNamespacedConfigMap(configMapName, namespace, configMap).pretty("true").execute();
                log.info("✅ ConfigMap updated: " + configMapName);
            } else {
                log.error("❌ Error creating/updating ConfigMap: " + e.getResponseBody());
                // Handle other exceptions as needed
            }
        }
    }




    @Override
    public List<BuildEnvironmentBO> getEnvByPermission(String projectIds, String spaceId) {
        String[] split = projectIds.split(",");
        List<BuildEnvironmentDO> result = new ArrayList<>();
        List<BuildEnvironmentDO> list = buildEnvironmentAtomService.list();
        for (BuildEnvironmentDO buildEnvironmentDO : list) {
            if (buildEnvironmentDO.getEnvironmentSource().equals("platform")) {
                result.add(buildEnvironmentDO);
            } else {
                if (buildEnvironmentDO.getSpaceAuth()) {
                    result.add(buildEnvironmentDO);
                    continue;
                } else if (buildEnvironmentDO.getProjectAuth() && split.length > 0) {
                    result.add(buildEnvironmentDO);
                    continue;
                } else {
                    LambdaQueryWrapper<BuildEnvironmentPermissionDO> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(BuildEnvironmentPermissionDO::getEnvironmentId, buildEnvironmentDO.getId());
                    queryWrapper.eq(BuildEnvironmentPermissionDO::getType, "space");
                    queryWrapper.eq(BuildEnvironmentPermissionDO::getBusinessDataId, spaceId);
                    long count = buildEnvironmentPermissionAtomService.count(queryWrapper);
                    if (count > 0) {
                        result.add(buildEnvironmentDO);
                    } else {
                        LambdaQueryWrapper<BuildEnvironmentPermissionDO> wrapper = new LambdaQueryWrapper<>();
                        wrapper.eq(BuildEnvironmentPermissionDO::getEnvironmentId, buildEnvironmentDO.getId());
                        wrapper.eq(BuildEnvironmentPermissionDO::getType, "project");
                        wrapper.in(BuildEnvironmentPermissionDO::getBusinessDataId, split);
                        long count1 = buildEnvironmentPermissionAtomService.count(wrapper);
                        if (count1 > 0) {
                            result.add(buildEnvironmentDO);
                        }
                    }
                }
            }
        }
        return BeanCloner.clone(result, BuildEnvironmentBO.class);
    }


    @Override
    public PageResponse<List<EnvironmentVersionBO>> getBuildVersionList(PageRequest pageRequest, EnvironmentVersionBO environmentVersionBO) {
        return environmentVersionBizService.pageEnvironmentVersions(pageRequest, environmentVersionBO);
    }

}
