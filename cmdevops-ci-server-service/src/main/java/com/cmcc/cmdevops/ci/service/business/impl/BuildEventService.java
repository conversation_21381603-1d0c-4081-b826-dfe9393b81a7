package com.cmcc.cmdevops.ci.service.business.impl;

import com.cmcc.cmdevops.ci.service.bo.BuildSnapshotBO;
import com.cmcc.cmdevops.ci.service.bo.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
public class BuildEventService {
//    @Qualifier("buildExecutionPool")
    private ThreadPoolTaskExecutor buildExecutionPool;

//    @Resource
//    private PipelineNotificationSubscriptionsService pipelineNotificationSubscriptionsService;

    public void BuildStatusChangedEvent(BuildSnapshotBO buildSnapshotBO, StatusEnum status) {
        CompletableFuture.runAsync(() -> {
            try {
                switch (status) {
                    case RUNNING:
                        pipelineStarted(buildSnapshotBO.getTaskId());
                        break;
                    case SUCCESS:
                        pipelineSuccess(buildSnapshotBO.getTaskId());
                        break;
                    case FAILED:
                        pipelineFailed(buildSnapshotBO.getTaskId());
                        break;
                    case PAUSED:
                        pipelinePaused(buildSnapshotBO.getTaskId());
                        break;
//                    case WAITING_APPROVAL:
//                        pipelinePaused(buildSnapshotBO.getTaskId());
//                        break;
                    default:
                        log.warn("Unknown status type: {}", status);
                }
            } catch (Exception e) {
                log.error("PipelineEventService.PipelineStatusChangedEvent error:", e);
                e.printStackTrace();
            }
        }, buildExecutionPool);
    }

    public void pipelineStarted(String pipelineId) {
        //  启动事件 发送通知
//        pipelineNotificationSubscriptionsService.pushSystemMessage(pipelineId,"start");
    }

    public void pipelineSuccess(String pipelineId) {
        //   成功事件 发送通知
//        pipelineNotificationSubscriptionsService.pushSystemMessage(pipelineId,"success");
    }

    public void pipelineFailed(String pipelineId) {
        //   失败事件 发送通知
//        pipelineNotificationSubscriptionsService.pushSystemMessage(pipelineId,"failure");
    }

    public void pipelinePaused(String pipelineId) {
        //   暂停事件 发送通知
//        pipelineNotificationSubscriptionsService.pushSystemMessage(pipelineId,"pause");
    }

    public void pipelineWattingApproval(String pipelineId) {
        //   审批事件 发送通知
//        pipelineNotificationSubscriptionsService.pushSystemMessage(pipelineId,"wattingApproval");
    }

}
