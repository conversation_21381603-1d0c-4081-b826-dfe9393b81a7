package com.cmcc.cmdevops.ci.service.business.util;

import com.alibaba.fastjson2.JSONPath;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class JsonUtil {

    private static final Logger log = LoggerFactory.getLogger(JsonUtil.class);

    /**
     * 根据JSONPath路径从对象中提取指定类型的值
     *
     * @param obj 源对象
     * @param path JSONPath表达式
     * @param clazz 目标类型的Class对象
     * @param defaultVal 默认值（当路径不存在或值为null时返回）
     * @param <T> 返回值类型
     * @return 提取的值或默认值
     */
    public static <T> T evalByPath(Object obj, String path, Class<T> clazz, T defaultVal) {
        try {
            Object result = JSONPath.eval(obj, path);
            if (result == null) {
                return defaultVal;
            }

            // 类型检查和转换
            if (clazz.isInstance(result)) {
                return clazz.cast(result);
            }

            // 尝试类型转换
            return convertValue(result, clazz, defaultVal);

        } catch (Exception e) {
            log.warn("JSONPath解析失败，path: {}, error: {}", path, e.getMessage());
            return defaultVal;
        }
    }

    /**
     * 根据JSONPath路径从对象中提取值（自动推断类型）
     *
     * @param obj 源对象
     * @param path JSONPath表达式
     * @param defaultVal 默认值
     * @param <T> 返回值类型
     * @return 提取的值或默认值
     */
    @SuppressWarnings("unchecked")
    public static <T> T evalByPath(Object obj, String path, T defaultVal) {
        try {
            Object result = JSONPath.eval(obj, path);
            if (result == null) {
                return defaultVal;
            }

            if (defaultVal != null && defaultVal.getClass().isInstance(result)) {
                return (T) result;
            }

            // 如果类型不匹配，尝试转换
            if (defaultVal != null) {
                return convertValue(result, (Class<T>) defaultVal.getClass(), defaultVal);
            }

            return (T) result;

        } catch (Exception e) {
            log.warn("JSONPath解析失败，path: {}, error: {}", path, e.getMessage());
            return defaultVal;
        }
    }

    /**
     * 值类型转换
     *
     * @param value 原始值
     * @param targetClass 目标类型
     * @param defaultVal 默认值
     * @param <T> 目标类型
     * @return 转换后的值或默认值
     */
    @SuppressWarnings("unchecked")
    public static <T> T convertValue(Object value, Class<T> targetClass, T defaultVal) {
        if (value == null) {
            return defaultVal;
        }

        try {
            // String类型转换
            if (targetClass == String.class) {
                return (T) String.valueOf(value);
            }

            // Integer类型转换
            if (targetClass == Integer.class) {
                if (value instanceof Number) {
                    return (T) Integer.valueOf(((Number) value).intValue());
                }
                if (value instanceof String) {
                    return (T) Integer.valueOf((String) value);
                }
            }

            // Long类型转换
            if (targetClass == Long.class) {
                if (value instanceof Number) {
                    return (T) Long.valueOf(((Number) value).longValue());
                }
                if (value instanceof String) {
                    return (T) Long.valueOf((String) value);
                }
            }

            // Boolean类型转换
            if (targetClass == Boolean.class) {
                if (value instanceof Boolean) {
                    return (T) value;
                }
                if (value instanceof String) {
                    return (T) Boolean.valueOf((String) value);
                }
            }

            // 直接返回原值（如果类型兼容）
            if (targetClass.isInstance(value)) {
                return targetClass.cast(value);
            }

        } catch (Exception e) {
            log.warn("类型转换失败，value: {}, targetClass: {}, error: {}",
                    value, targetClass.getSimpleName(), e.getMessage());
        }

        return defaultVal;
    }
}
