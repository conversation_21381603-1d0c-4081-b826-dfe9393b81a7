package com.cmcc.cmdevops.ci.service.dao;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2025-05-26
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("build_task")
public class BuildTaskDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private String id;

    @TableField("task_name")
    private String taskName;

    /**
     * PENDING(0, "待执行"),
     * QUEUED(1, "排队中"),
     * RUNNING(2, "执行中"),
     * SUCCESS(3, "成功"),
     * FAILED(4, "失败"),
     * CANCELLED(5, "已取消"),
     * TIMEOUT(6, "已暂停"),
     * SKIPPED(7, "已跳过"),
     * PAUSED(8, "超时");
     * STOP(9, "已停止");
     */
    @TableField("build_status")
    private String buildStatus;

    @TableField("build_type")
    private String buildType;

    @TableField("build_number")
    private String buildNumber;

    @TableField("last_build_id")
    private String lastBuildId;

    @TableField("last_build_time")
    private LocalDateTime lastBuildTime;

    @TableField("has_disable")
    private Boolean hasDisable;

    @TableField("space_id")
    private String spaceId;

    @TableField("tenant_id")
    private String tenantId;

    @TableField("deleted")
    @TableLogic
    private Boolean deleted;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField("create_uid")
    private String createUid;

    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @TableField("update_uid")
    private String updateUid;

    @TableField("delete_time")
    private LocalDateTime deleteTime;

    @TableField("delete_uid")
    private String deleteUid;

    @TableField("build_code_type")
    private String buildCodeType;

    @TableField("file_id")
    private String fileId;

    @TableField("build_source")
    private String buildSource;

    @TableField("permission_type")
    private String permissionType;

    @TableField("concurrent_strategy")
    private Integer concurrentStrategy;

    @TableField("max_concurrent_count")
    private Integer maxConcurrentCount;


    @TableField("assigned_node")
    private Integer assignedNode;

    @TableField("overtime")
    private Short overtime;

    @TableField("system_code")
    private String systemCode;
}
