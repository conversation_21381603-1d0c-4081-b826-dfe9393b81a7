package com.cmcc.cmdevops.ci.service.atom.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cmcc.cmdevops.ci.service.atom.BuildEnvironmentMonitorAtomService;
import com.cmcc.cmdevops.ci.service.dao.BuildEnvironmentMonitorDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildEnvironmentMonitorMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> @since 2025-05-22
 */
@Service
@RequiredArgsConstructor
public class BuildEnvironmentMonitorAtomServiceImpl extends ServiceImpl<BuildEnvironmentMonitorMapper, BuildEnvironmentMonitorDO> implements BuildEnvironmentMonitorAtomService {

    private final BuildEnvironmentMonitorMapper buildEnvironmentMonitorMapper;

    @Override
    public List<BuildEnvironmentMonitorDO> listLatestByEnvId(Integer envId) {
        return buildEnvironmentMonitorMapper.listLatestByEnvId(envId);
    }
}
