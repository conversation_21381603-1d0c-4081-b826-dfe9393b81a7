<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmcc.cmdevops.ci.service.dao.mapper.BuildEnvironmentStorageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cmcc.cmdevops.ci.service.dao.BuildEnvironmentStorageDO">
        <id column="id" property="id" />
        <result column="environment_id" property="environmentId" />
        <result column="task_id" property="taskId" />
        <result column="used_storage" property="usedStorage" />
        <result column="space_id" property="spaceId" />
        <result column="tenant_id" property="tenantId" />
        <result column="deleted" property="deleted" />
        <result column="create_uid" property="createUid" />
        <result column="create_time" property="createTime" />
        <result column="update_uid" property="updateUid" />
        <result column="update_time" property="updateTime" />
        <result column="delete_uid" property="deleteUid" />
        <result column="delete_time" property="deleteTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, environment_id, task_id, used_storage, space_id, tenant_id, deleted,
        create_time, create_uid, update_time, update_uid, delete_time, delete_uid
    </sql>

</mapper>
