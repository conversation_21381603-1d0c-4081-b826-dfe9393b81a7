package com.cmcc.cmdevops.ci.config;

import com.baomidou.mybatisplus.autoconfigure.SpringBootVFS;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.MybatisXMLLanguageDriver;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.transaction.SpringManagedTransactionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;

/**
 * 数据源配置
 * 如果要多个数据源或者数据源配置前缀，需要定义相应的properties类，然后注入即可
 * 注意，mapper扫描不能含有service、component等注解
 * <AUTHOR>
 */
@Configuration
@MapperScan(basePackages = {"com.cmcc.cmdevops.ci.service.dao.*"})
public class DataSourceConfig {
    /**
     * 如果是多数据源，可以指定相应的路径
     */
    private static final String CLASSPATH_MYBATIS_XML = "classpath*:com/cmcc/cmdevops/ci/service/mapper/xml/*.xml";

    @Value("${mybatis.sql.log-enabled:false}")
    private boolean sqlLogEnabled;

    @Value("${mybatis.sql.log-prefix:mybatis.sql}")
    private String sqlLogPrefix;

    @Bean(name = "globalConfig")
    public GlobalConfig globalConfig(MetaObjectHandler metaObjectHandler) {
        GlobalConfig globalConfig = new GlobalConfig();
        globalConfig.setMetaObjectHandler(metaObjectHandler);
        globalConfig.setBanner(false);
        return globalConfig;
    }

    @Bean
    public SqlSessionFactory sqlSessionFactory(@Qualifier("dynamicDataSource") DataSource dataSource,
                                               MybatisPlusInterceptor mybatisPlusInterceptor,
                                               GlobalConfig globalConfig) throws Exception {
        MybatisSqlSessionFactoryBean factoryBean = new MybatisSqlSessionFactoryBean();
        factoryBean.setPlugins(mybatisPlusInterceptor);
        factoryBean.setDataSource(dataSource);
        factoryBean.setVfs(SpringBootVFS.class);

        MybatisConfiguration configuration = new MybatisConfiguration();
        configuration.setDefaultScriptingLanguage(MybatisXMLLanguageDriver.class);
        configuration.setJdbcTypeForNull(JdbcType.NULL);
        // 根据配置决定是否启用SQL日志
        if (sqlLogEnabled) {
            configuration.setLogImpl(org.apache.ibatis.logging.stdout.StdOutImpl.class);
            configuration.setLogPrefix(sqlLogPrefix);
        }

        factoryBean.setConfiguration(configuration);
        factoryBean.setTransactionFactory(new SpringManagedTransactionFactory());
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        factoryBean.setMapperLocations(resolver.getResources(CLASSPATH_MYBATIS_XML));
        factoryBean.setGlobalConfig(globalConfig);

        return factoryBean.getObject();
    }

    @Bean
    public SqlSessionTemplate sqlSessionTemplate(SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}
