package com.cmcc.cmdevops.ci.service.dao;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2025-08-05
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("environment_version")
public class EnvironmentVersionDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("environment_id")
    private Integer environmentId;

    @TableField("op_type")
    private String opType;

    @TableField("op_object")
    private String opObject;

    @TableField("old_version")
    private String oldVersion;

    @TableField("new_version")
    private String newVersion;

    @TableField("tenant_id")
    private String tenantId;

    @TableLogic
    @TableField("deleted")
    private Boolean deleted;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField("create_uid")
    private String createUid;

    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableField("update_uid")
    private String updateUid;

    @TableField("delete_time")
    private LocalDateTime deleteTime;

    @TableField("delete_uid")
    private String deleteUid;
}
