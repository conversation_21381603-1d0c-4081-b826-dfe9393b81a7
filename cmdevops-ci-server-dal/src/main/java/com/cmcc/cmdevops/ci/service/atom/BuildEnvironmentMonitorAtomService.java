package com.cmcc.cmdevops.ci.service.atom;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cmcc.cmdevops.ci.service.dao.BuildEnvironmentMonitorDO;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> @since 2025-05-22
 */
public interface BuildEnvironmentMonitorAtomService extends IService<BuildEnvironmentMonitorDO> {

    List<BuildEnvironmentMonitorDO> listLatestByEnvId(Integer envId);
}
