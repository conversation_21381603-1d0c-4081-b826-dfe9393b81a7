package com.cmcc.cmdevops.ci.service.atom.impl;

import com.cmcc.cmdevops.ci.service.dao.EnvironmentVersionDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.EnvironmentVersionMapper;
import com.cmcc.cmdevops.ci.service.atom.EnvironmentVersionAtomService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> @since 2025-08-05
 */
@Service
public class EnvironmentVersionAtomServiceImpl extends ServiceImpl<EnvironmentVersionMapper, EnvironmentVersionDO> implements EnvironmentVersionAtomService {

}
