package com.cmcc.cmdevops.ci.service.atom.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cmcc.cmdevops.ci.service.atom.BuildEnvironmentStorageAtomService;
import com.cmcc.cmdevops.ci.service.dao.BuildEnvironmentStorageDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildEnvironmentStorageMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> @since 2025-05-22
 */
@Service
public class BuildEnvironmentStorageAtomServiceImpl extends ServiceImpl<BuildEnvironmentStorageMapper, BuildEnvironmentStorageDO> implements BuildEnvironmentStorageAtomService {

}
