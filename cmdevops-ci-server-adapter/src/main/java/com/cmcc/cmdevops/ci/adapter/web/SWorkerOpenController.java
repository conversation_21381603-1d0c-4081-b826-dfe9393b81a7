package com.cmcc.cmdevops.ci.adapter.web;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.cmcc.cmdevops.BaseResponse;
import com.cmcc.cmdevops.ci.service.business.SWorkerOpenBizService;
import com.cmcc.cmdevops.ci.service.business.util.UserUtils;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/sw")
// 软工开发工具
public class SWorkerOpenController {
    @Resource
    private SWorkerOpenBizService sWorkerOpenBizService;


    @GetMapping("/getImageRepository/{spaceId}")
    public BaseResponse<JSONArray> getImageRepository(@PathVariable String spaceId) {
        String tenantId = UserUtils.getTenantId();
        JSONArray result = sWorkerOpenBizService.getImageRepository(tenantId, spaceId);
        return BaseResponse.success(result);
    }

    @GetMapping("/getNormalRepository/{spaceId}")
    public BaseResponse<JSONArray> getNormalRepository(@PathVariable String spaceId) {
        String tenantId = UserUtils.getTenantId();
        JSONArray result = sWorkerOpenBizService.getNormalRepository(tenantId, spaceId);
        return BaseResponse.success(result);
    }


    @GetMapping("/getImageArtifacts/{repoKey}")
    public BaseResponse<JSONArray> getImageArtifacts(@PathVariable String repoKey) {
        String tenantId = UserUtils.getTenantId();
        JSONArray result = sWorkerOpenBizService.getImageArtifacts(tenantId, repoKey);
        return BaseResponse.success(result);
    }

    @GetMapping("/getNormalArtifacts/{repoKey}")
    public BaseResponse<JSONArray> getNormalArtifacts(@PathVariable String repoKey) {
        String tenantId = UserUtils.getTenantId();
        JSONArray result = sWorkerOpenBizService.getNormalArtifacts(tenantId, repoKey);
        return BaseResponse.success(result);
    }


    @GetMapping("/getImageVersions/{repoKey}/{artiName}")
    public BaseResponse<JSONArray> getImageVersions(@PathVariable String repoKey, @PathVariable String artiName) {
        String tenantId = UserUtils.getTenantId();
        JSONArray result = sWorkerOpenBizService.getImageVersions(tenantId, repoKey, artiName);
        return BaseResponse.success(result);
    }

    @GetMapping("/getNormalVersions/{repoKey}/{artiName}")
    public BaseResponse<JSONArray> getNormalVersions(@PathVariable String repoKey, @PathVariable String artiName) {
        String tenantId = UserUtils.getTenantId();
        JSONArray result = sWorkerOpenBizService.getNormalVersions(tenantId, repoKey, artiName);
        return BaseResponse.success(result);
    }


    @GetMapping("/getFileContent/{projectId}/{filePath}")
    public BaseResponse<String> getFileContent(@PathVariable String projectId,
                                               @PathVariable String userId,
                                               @PathVariable String filePath,
                                               @PathVariable String ref) {
        String result = sWorkerOpenBizService.getFileContent("", projectId, userId, filePath, ref);
        return BaseResponse.success(result);
    }
    // 查询分支列表

    @GetMapping("/getBranchList/{projectId}")
    public BaseResponse<JSONArray> getBranchList(@PathVariable String projectId) {
        String userId = UserUtils.getUserId();
        String tenantId = UserUtils.getTenantId();
        JSONArray result = sWorkerOpenBizService.getBranchList(tenantId, projectId, userId);
        return BaseResponse.success(result);
    }


    // 代码仓库列表
    @GetMapping("/getCodeRepositoryList/{spaceId}")
    public BaseResponse<JSONArray> getCodeRepositoryList(@PathVariable String spaceId) {
        String userId = UserUtils.getUserId();
        String tenantId = UserUtils.getTenantId();
        JSONArray result = sWorkerOpenBizService.getCodeRepositoryList(tenantId, spaceId, userId);
        return BaseResponse.success(result);
    }


    // 获取TAG列表
    @GetMapping("/getTagList/{projectId}")
    public BaseResponse<JSONArray> getTagList(@PathVariable String projectId) {
        String userId = UserUtils.getUserId();
        String tenantId = UserUtils.getTenantId();
        JSONArray result = sWorkerOpenBizService.getTagList(tenantId, projectId, userId);
        return BaseResponse.success(result);
    }

    //获取研发空间用户列表
    @GetMapping("/getUserList/{spaceId}")
    public BaseResponse<JSONArray> getUserList(@PathVariable String spaceId) {
        String tenantId = UserUtils.getTenantId();
        JSONArray result = sWorkerOpenBizService.getUserList(tenantId, spaceId);
        return BaseResponse.success(result);
    }

    //获取代码仓库的元数据
    @GetMapping("/getCodeRepoData/{projectId}")
    public BaseResponse<JSONObject> getCodeRepoData(@PathVariable String projectId) {
        String tenantId = UserUtils.getTenantId();
        JSONObject result = sWorkerOpenBizService.getCodeRepoMetaData(projectId, tenantId);
        return BaseResponse.success(result);
    }

    @GetMapping("/queryAccelerationNode/{source}")
    public BaseResponse<JSONArray> queryAccelerationNode(@PathVariable String source) {
        String tenantId = UserUtils.getTenantId();
        JSONArray result = sWorkerOpenBizService.queryAccelerationNode(source, tenantId);
        return BaseResponse.success(result);
    }

    @GetMapping("/querySpaceList")
    public BaseResponse<JSONArray> querySpaceList() {
        String tenantId = UserUtils.getTenantId();
        JSONArray result = sWorkerOpenBizService.querySpaceList(tenantId);
        return BaseResponse.success(result);
    }

    @GetMapping("/queryProjectList")
    public BaseResponse<JSONArray> queryProjectList() {
        String tenantId = UserUtils.getTenantId();
        JSONArray result = sWorkerOpenBizService.queryProjectList(tenantId);
        return BaseResponse.success(result);
    }

    @GetMapping("/queryProjectListBySpaceId/{spaceId}")
    public BaseResponse<JSONArray> queryProjectListBySpaceId(@PathVariable String spaceId) {
        String tenantId = UserUtils.getTenantId();
        JSONArray result = sWorkerOpenBizService.queryProjectListBySpaceId(tenantId, spaceId);
        return BaseResponse.success(result);
    }
}
