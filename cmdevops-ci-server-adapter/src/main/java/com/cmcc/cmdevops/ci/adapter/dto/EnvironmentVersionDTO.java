package com.cmcc.cmdevops.ci.adapter.dto;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2025-05-20
 */
@Getter
@Setter
@Accessors(chain = true)
public class EnvironmentVersionDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private Integer environmentId;

    private String opType;

    private String opObject;

    private String oldVersion;

    private String newVersion;

    private String tenantId;

    private Boolean deleted;

    private LocalDateTime createTime;

    private String createUid;

    private LocalDateTime updateTime;

    private String updateUid;

    private LocalDateTime deleteTime;

    private String deleteUid;

    private LocalDateTime startTime;

    private LocalDateTime endTime;
}
