package com.cmcc.cmdevops.ci.adapter.dto.request;


import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.ci.adapter.dto.BuildVersionDTO;
import com.cmcc.cmdevops.ci.adapter.dto.EnvironmentVersionDTO;

public class EnvironmentVersionRequest extends PageRequest {
    private EnvironmentVersionDTO environmentVersionDTO;

    public EnvironmentVersionDTO getEnvironmentVersionDTO() {
        return environmentVersionDTO;
    }

    public void setEnvironmentVersionDTO(EnvironmentVersionDTO environmentVersionDTO) {
        this.environmentVersionDTO = environmentVersionDTO;
    }
}
